(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))n(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&n(o)}).observe(document,{childList:!0,subtree:!0});function s(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function n(r){if(r.ep)return;r.ep=!0;const i=s(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function As(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const V={},tt=[],we=()=>{},Fr=()=>!1,Gt=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ps=e=>e.startsWith("onUpdate:"),Q=Object.assign,Rs=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Dr=Object.prototype.hasOwnProperty,H=(e,t)=>Dr.call(e,t),R=Array.isArray,st=e=>Jt(e)==="[object Map]",An=e=>Jt(e)==="[object Set]",I=e=>typeof e=="function",J=e=>typeof e=="string",Be=e=>typeof e=="symbol",z=e=>e!==null&&typeof e=="object",Pn=e=>(z(e)||I(e))&&I(e.then)&&I(e.catch),Rn=Object.prototype.toString,Jt=e=>Rn.call(e),Nr=e=>Jt(e).slice(8,-1),In=e=>Jt(e)==="[object Object]",Is=e=>J(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,pt=As(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Yt=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Hr=/-(\w)/g,je=Yt(e=>e.replace(Hr,(t,s)=>s?s.toUpperCase():"")),Lr=/\B([A-Z])/g,Ze=Yt(e=>e.replace(Lr,"-$1").toLowerCase()),Mn=Yt(e=>e.charAt(0).toUpperCase()+e.slice(1)),rs=Yt(e=>e?`on${Mn(e)}`:""),$e=(e,t)=>!Object.is(e,t),is=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},gs=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},$r=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let en;const Xt=()=>en||(en=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Zt(e){if(R(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],r=J(n)?Vr(n):Zt(n);if(r)for(const i in r)t[i]=r[i]}return t}else if(J(e)||z(e))return e}const jr=/;(?![^(]*\))/g,Ur=/:([^]+)/,Br=/\/\*[^]*?\*\//g;function Vr(e){const t={};return e.replace(Br,"").split(jr).forEach(s=>{if(s){const n=s.split(Ur);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Ms(e){let t="";if(J(e))t=e;else if(R(e))for(let s=0;s<e.length;s++){const n=Ms(e[s]);n&&(t+=n+" ")}else if(z(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Kr="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Wr=As(Kr);function Fn(e){return!!e||e===""}const Dn=e=>!!(e&&e.__v_isRef===!0),Ge=e=>J(e)?e:e==null?"":R(e)||z(e)&&(e.toString===Rn||!I(e.toString))?Dn(e)?Ge(e.value):JSON.stringify(e,Nn,2):String(e),Nn=(e,t)=>Dn(t)?Nn(e,t.value):st(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,r],i)=>(s[os(n,i)+" =>"]=r,s),{})}:An(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>os(s))}:Be(t)?os(t):z(t)&&!R(t)&&!In(t)?String(t):t,os=(e,t="")=>{var s;return Be(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let oe;class qr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=oe,!t&&oe&&(this.index=(oe.scopes||(oe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=oe;try{return oe=this,t()}finally{oe=s}}}on(){++this._on===1&&(this.prevScope=oe,oe=this)}off(){this._on>0&&--this._on===0&&(oe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(this.effects.length=0,s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function zr(){return oe}let B;const ls=new WeakSet;class Hn{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,oe&&oe.active&&oe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ls.has(this)&&(ls.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||$n(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,tn(this),jn(this);const t=B,s=ae;B=this,ae=!0;try{return this.fn()}finally{Un(this),B=t,ae=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ns(t);this.deps=this.depsTail=void 0,tn(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ls.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_s(this)&&this.run()}get dirty(){return _s(this)}}let Ln=0,gt,_t;function $n(e,t=!1){if(e.flags|=8,t){e.next=_t,_t=e;return}e.next=gt,gt=e}function Fs(){Ln++}function Ds(){if(--Ln>0)return;if(_t){let t=_t;for(_t=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;gt;){let t=gt;for(gt=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function jn(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Un(e){let t,s=e.depsTail,n=s;for(;n;){const r=n.prevDep;n.version===-1?(n===s&&(s=r),Ns(n),Gr(n)):t=n,n.dep.activeLink=n.prevActiveLink,n.prevActiveLink=void 0,n=r}e.deps=t,e.depsTail=s}function _s(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Bn(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Bn(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===St)||(e.globalVersion=St,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!_s(e))))return;e.flags|=2;const t=e.dep,s=B,n=ae;B=e,ae=!0;try{jn(e);const r=e.fn(e._value);(t.version===0||$e(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{B=s,ae=n,Un(e),e.flags&=-3}}function Ns(e,t=!1){const{dep:s,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n,!n&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)Ns(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function Gr(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let ae=!0;const Vn=[];function Re(){Vn.push(ae),ae=!1}function Ie(){const e=Vn.pop();ae=e===void 0?!0:e}function tn(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=B;B=void 0;try{t()}finally{B=s}}}let St=0;class Jr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Hs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!B||!ae||B===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==B)s=this.activeLink=new Jr(B,this),B.deps?(s.prevDep=B.depsTail,B.depsTail.nextDep=s,B.depsTail=s):B.deps=B.depsTail=s,Kn(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=B.depsTail,s.nextDep=void 0,B.depsTail.nextDep=s,B.depsTail=s,B.deps===s&&(B.deps=n)}return s}trigger(t){this.version++,St++,this.notify(t)}notify(t){Fs();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Ds()}}}function Kn(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Kn(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const ms=new WeakMap,Ye=Symbol(""),bs=Symbol(""),wt=Symbol("");function X(e,t,s){if(ae&&B){let n=ms.get(e);n||ms.set(e,n=new Map);let r=n.get(s);r||(n.set(s,r=new Hs),r.map=n,r.key=s),r.track()}}function Pe(e,t,s,n,r,i){const o=ms.get(e);if(!o){St++;return}const c=u=>{u&&u.trigger()};if(Fs(),t==="clear")o.forEach(c);else{const u=R(e),h=u&&Is(s);if(u&&s==="length"){const a=Number(n);o.forEach((p,v)=>{(v==="length"||v===wt||!Be(v)&&v>=a)&&c(p)})}else switch((s!==void 0||o.has(void 0))&&c(o.get(s)),h&&c(o.get(wt)),t){case"add":u?h&&c(o.get("length")):(c(o.get(Ye)),st(e)&&c(o.get(bs)));break;case"delete":u||(c(o.get(Ye)),st(e)&&c(o.get(bs)));break;case"set":st(e)&&c(o.get(Ye));break}}Ds()}function Qe(e){const t=N(e);return t===e?t:(X(t,"iterate",wt),de(e)?t:t.map(te))}function Ls(e){return X(e=N(e),"iterate",wt),e}const Yr={__proto__:null,[Symbol.iterator](){return cs(this,Symbol.iterator,te)},concat(...e){return Qe(this).concat(...e.map(t=>R(t)?Qe(t):t))},entries(){return cs(this,"entries",e=>(e[1]=te(e[1]),e))},every(e,t){return Ee(this,"every",e,t,void 0,arguments)},filter(e,t){return Ee(this,"filter",e,t,s=>s.map(te),arguments)},find(e,t){return Ee(this,"find",e,t,te,arguments)},findIndex(e,t){return Ee(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ee(this,"findLast",e,t,te,arguments)},findLastIndex(e,t){return Ee(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ee(this,"forEach",e,t,void 0,arguments)},includes(...e){return fs(this,"includes",e)},indexOf(...e){return fs(this,"indexOf",e)},join(e){return Qe(this).join(e)},lastIndexOf(...e){return fs(this,"lastIndexOf",e)},map(e,t){return Ee(this,"map",e,t,void 0,arguments)},pop(){return ut(this,"pop")},push(...e){return ut(this,"push",e)},reduce(e,...t){return sn(this,"reduce",e,t)},reduceRight(e,...t){return sn(this,"reduceRight",e,t)},shift(){return ut(this,"shift")},some(e,t){return Ee(this,"some",e,t,void 0,arguments)},splice(...e){return ut(this,"splice",e)},toReversed(){return Qe(this).toReversed()},toSorted(e){return Qe(this).toSorted(e)},toSpliced(...e){return Qe(this).toSpliced(...e)},unshift(...e){return ut(this,"unshift",e)},values(){return cs(this,"values",te)}};function cs(e,t,s){const n=Ls(e),r=n[t]();return n!==e&&!de(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=s(i.value)),i}),r}const Xr=Array.prototype;function Ee(e,t,s,n,r,i){const o=Ls(e),c=o!==e&&!de(e),u=o[t];if(u!==Xr[t]){const p=u.apply(e,i);return c?te(p):p}let h=s;o!==e&&(c?h=function(p,v){return s.call(this,te(p),v,e)}:s.length>2&&(h=function(p,v){return s.call(this,p,v,e)}));const a=u.call(o,h,n);return c&&r?r(a):a}function sn(e,t,s,n){const r=Ls(e);let i=s;return r!==e&&(de(e)?s.length>3&&(i=function(o,c,u){return s.call(this,o,c,u,e)}):i=function(o,c,u){return s.call(this,o,te(c),u,e)}),r[t](i,...n)}function fs(e,t,s){const n=N(e);X(n,"iterate",wt);const r=n[t](...s);return(r===-1||r===!1)&&Bs(s[0])?(s[0]=N(s[0]),n[t](...s)):r}function ut(e,t,s=[]){Re(),Fs();const n=N(e)[t].apply(e,s);return Ds(),Ie(),n}const Zr=As("__proto__,__v_isRef,__isVue"),Wn=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Be));function Qr(e){Be(e)||(e=String(e));const t=N(this);return X(t,"has",e),t.hasOwnProperty(e)}class qn{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return i;if(s==="__v_raw")return n===(r?i?ci:Yn:i?Jn:Gn).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const o=R(t);if(!r){let u;if(o&&(u=Yr[s]))return u;if(s==="hasOwnProperty")return Qr}const c=Reflect.get(t,s,Z(t)?t:n);return(Be(s)?Wn.has(s):Zr(s))||(r||X(t,"get",s),i)?c:Z(c)?o&&Is(s)?c:c.value:z(c)?r?Xn(c):js(c):c}}class zn extends qn{constructor(t=!1){super(!1,t)}set(t,s,n,r){let i=t[s];if(!this._isShallow){const u=Xe(i);if(!de(n)&&!Xe(n)&&(i=N(i),n=N(n)),!R(t)&&Z(i)&&!Z(n))return u?!1:(i.value=n,!0)}const o=R(t)&&Is(s)?Number(s)<t.length:H(t,s),c=Reflect.set(t,s,n,Z(t)?t:r);return t===N(r)&&(o?$e(n,i)&&Pe(t,"set",s,n):Pe(t,"add",s,n)),c}deleteProperty(t,s){const n=H(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&n&&Pe(t,"delete",s,void 0),r}has(t,s){const n=Reflect.has(t,s);return(!Be(s)||!Wn.has(s))&&X(t,"has",s),n}ownKeys(t){return X(t,"iterate",R(t)?"length":Ye),Reflect.ownKeys(t)}}class kr extends qn{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const ei=new zn,ti=new kr,si=new zn(!0);const vs=e=>e,Dt=e=>Reflect.getPrototypeOf(e);function ni(e,t,s){return function(...n){const r=this.__v_raw,i=N(r),o=st(i),c=e==="entries"||e===Symbol.iterator&&o,u=e==="keys"&&o,h=r[e](...n),a=s?vs:t?ys:te;return!t&&X(i,"iterate",u?bs:Ye),{next(){const{value:p,done:v}=h.next();return v?{value:p,done:v}:{value:c?[a(p[0]),a(p[1])]:a(p),done:v}},[Symbol.iterator](){return this}}}}function Nt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ri(e,t){const s={get(r){const i=this.__v_raw,o=N(i),c=N(r);e||($e(r,c)&&X(o,"get",r),X(o,"get",c));const{has:u}=Dt(o),h=t?vs:e?ys:te;if(u.call(o,r))return h(i.get(r));if(u.call(o,c))return h(i.get(c));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&X(N(r),"iterate",Ye),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=N(i),c=N(r);return e||($e(r,c)&&X(o,"has",r),X(o,"has",c)),r===c?i.has(r):i.has(r)||i.has(c)},forEach(r,i){const o=this,c=o.__v_raw,u=N(c),h=t?vs:e?ys:te;return!e&&X(u,"iterate",Ye),c.forEach((a,p)=>r.call(i,h(a),h(p),o))}};return Q(s,e?{add:Nt("add"),set:Nt("set"),delete:Nt("delete"),clear:Nt("clear")}:{add(r){!t&&!de(r)&&!Xe(r)&&(r=N(r));const i=N(this);return Dt(i).has.call(i,r)||(i.add(r),Pe(i,"add",r,r)),this},set(r,i){!t&&!de(i)&&!Xe(i)&&(i=N(i));const o=N(this),{has:c,get:u}=Dt(o);let h=c.call(o,r);h||(r=N(r),h=c.call(o,r));const a=u.call(o,r);return o.set(r,i),h?$e(i,a)&&Pe(o,"set",r,i):Pe(o,"add",r,i),this},delete(r){const i=N(this),{has:o,get:c}=Dt(i);let u=o.call(i,r);u||(r=N(r),u=o.call(i,r)),c&&c.call(i,r);const h=i.delete(r);return u&&Pe(i,"delete",r,void 0),h},clear(){const r=N(this),i=r.size!==0,o=r.clear();return i&&Pe(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=ni(r,e,t)}),s}function $s(e,t){const s=ri(e,t);return(n,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?n:Reflect.get(H(s,r)&&r in n?s:n,r,i)}const ii={get:$s(!1,!1)},oi={get:$s(!1,!0)},li={get:$s(!0,!1)};const Gn=new WeakMap,Jn=new WeakMap,Yn=new WeakMap,ci=new WeakMap;function fi(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ui(e){return e.__v_skip||!Object.isExtensible(e)?0:fi(Nr(e))}function js(e){return Xe(e)?e:Us(e,!1,ei,ii,Gn)}function ai(e){return Us(e,!1,si,oi,Jn)}function Xn(e){return Us(e,!0,ti,li,Yn)}function Us(e,t,s,n,r){if(!z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=ui(e);if(i===0)return e;const o=r.get(e);if(o)return o;const c=new Proxy(e,i===2?n:s);return r.set(e,c),c}function mt(e){return Xe(e)?mt(e.__v_raw):!!(e&&e.__v_isReactive)}function Xe(e){return!!(e&&e.__v_isReadonly)}function de(e){return!!(e&&e.__v_isShallow)}function Bs(e){return e?!!e.__v_raw:!1}function N(e){const t=e&&e.__v_raw;return t?N(t):e}function di(e){return!H(e,"__v_skip")&&Object.isExtensible(e)&&gs(e,"__v_skip",!0),e}const te=e=>z(e)?js(e):e,ys=e=>z(e)?Xn(e):e;function Z(e){return e?e.__v_isRef===!0:!1}function at(e){return hi(e,!1)}function hi(e,t){return Z(e)?e:new pi(e,t)}class pi{constructor(t,s){this.dep=new Hs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:N(t),this._value=s?t:te(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||de(t)||Xe(t);t=n?t:N(t),$e(t,s)&&(this._rawValue=t,this._value=n?t:te(t),this.dep.trigger())}}function gi(e){return Z(e)?e.value:e}const _i={get:(e,t,s)=>t==="__v_raw"?e:gi(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const r=e[t];return Z(r)&&!Z(s)?(r.value=s,!0):Reflect.set(e,t,s,n)}};function Zn(e){return mt(e)?e:new Proxy(e,_i)}class mi{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Hs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=St-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&B!==this)return $n(this,!0),!0}get value(){const t=this.dep.track();return Bn(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function bi(e,t,s=!1){let n,r;return I(e)?n=e:(n=e.get,r=e.set),new mi(n,r,s)}const Ht={},Vt=new WeakMap;let Je;function vi(e,t=!1,s=Je){if(s){let n=Vt.get(s);n||Vt.set(s,n=[]),n.push(e)}}function yi(e,t,s=V){const{immediate:n,deep:r,once:i,scheduler:o,augmentJob:c,call:u}=s,h=O=>r?O:de(O)||r===!1||r===0?Le(O,1):Le(O);let a,p,v,x,D=!1,F=!1;if(Z(e)?(p=()=>e.value,D=de(e)):mt(e)?(p=()=>h(e),D=!0):R(e)?(F=!0,D=e.some(O=>mt(O)||de(O)),p=()=>e.map(O=>{if(Z(O))return O.value;if(mt(O))return h(O);if(I(O))return u?u(O,2):O()})):I(e)?t?p=u?()=>u(e,2):e:p=()=>{if(v){Re();try{v()}finally{Ie()}}const O=Je;Je=a;try{return u?u(e,3,[x]):e(x)}finally{Je=O}}:p=we,t&&r){const O=p,G=r===!0?1/0:r;p=()=>Le(O(),G)}const Y=zr(),$=()=>{a.stop(),Y&&Y.active&&Rs(Y.effects,a)};if(i&&t){const O=t;t=(...G)=>{O(...G),$()}}let W=F?new Array(e.length).fill(Ht):Ht;const q=O=>{if(!(!(a.flags&1)||!a.dirty&&!O))if(t){const G=a.run();if(r||D||(F?G.some((Fe,he)=>$e(Fe,W[he])):$e(G,W))){v&&v();const Fe=Je;Je=a;try{const he=[G,W===Ht?void 0:F&&W[0]===Ht?[]:W,x];W=G,u?u(t,3,he):t(...he)}finally{Je=Fe}}}else a.run()};return c&&c(q),a=new Hn(p),a.scheduler=o?()=>o(q,!1):q,x=O=>vi(O,!1,a),v=a.onStop=()=>{const O=Vt.get(a);if(O){if(u)u(O,4);else for(const G of O)G();Vt.delete(a)}},t?n?q(!0):W=a.run():o?o(q.bind(null,!0),!0):a.run(),$.pause=a.pause.bind(a),$.resume=a.resume.bind(a),$.stop=$,$}function Le(e,t=1/0,s){if(t<=0||!z(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Z(e))Le(e.value,t,s);else if(R(e))for(let n=0;n<e.length;n++)Le(e[n],t,s);else if(An(e)||st(e))e.forEach(n=>{Le(n,t,s)});else if(In(e)){for(const n in e)Le(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Le(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ot(e,t,s,n){try{return n?e(...n):e()}catch(r){Qt(r,t,s)}}function Te(e,t,s,n){if(I(e)){const r=Ot(e,t,s,n);return r&&Pn(r)&&r.catch(i=>{Qt(i,t,s)}),r}if(R(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Te(e[i],t,s,n));return r}}function Qt(e,t,s,n=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||V;if(t){let c=t.parent;const u=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const a=c.ec;if(a){for(let p=0;p<a.length;p++)if(a[p](e,u,h)===!1)return}c=c.parent}if(i){Re(),Ot(i,null,10,[e,u,h]),Ie();return}}xi(e,s,r,n,o)}function xi(e,t,s,n=!0,r=!1){if(r)throw e;console.error(e)}const se=[];let ye=-1;const nt=[];let Ne=null,et=0;const Qn=Promise.resolve();let Kt=null;function Si(e){const t=Kt||Qn;return e?t.then(this?e.bind(this):e):t}function wi(e){let t=ye+1,s=se.length;for(;t<s;){const n=t+s>>>1,r=se[n],i=Ct(r);i<e||i===e&&r.flags&2?t=n+1:s=n}return t}function Vs(e){if(!(e.flags&1)){const t=Ct(e),s=se[se.length-1];!s||!(e.flags&2)&&t>=Ct(s)?se.push(e):se.splice(wi(t),0,e),e.flags|=1,kn()}}function kn(){Kt||(Kt=Qn.then(tr))}function Ci(e){R(e)?nt.push(...e):Ne&&e.id===-1?Ne.splice(et+1,0,e):e.flags&1||(nt.push(e),e.flags|=1),kn()}function nn(e,t,s=ye+1){for(;s<se.length;s++){const n=se[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;se.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function er(e){if(nt.length){const t=[...new Set(nt)].sort((s,n)=>Ct(s)-Ct(n));if(nt.length=0,Ne){Ne.push(...t);return}for(Ne=t,et=0;et<Ne.length;et++){const s=Ne[et];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Ne=null,et=0}}const Ct=e=>e.id==null?e.flags&2?-1:1/0:e.id;function tr(e){try{for(ye=0;ye<se.length;ye++){const t=se[ye];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ot(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ye<se.length;ye++){const t=se[ye];t&&(t.flags&=-2)}ye=-1,se.length=0,er(),Kt=null,(se.length||nt.length)&&tr()}}let Se=null,sr=null;function Wt(e){const t=Se;return Se=e,sr=e&&e.type.__scopeId||null,t}function Ti(e,t=Se,s){if(!t||e._n)return e;const n=(...r)=>{n._d&&hn(-1);const i=Wt(t);let o;try{o=e(...r)}finally{Wt(i),n._d&&hn(1)}return o};return n._n=!0,n._c=!0,n._d=!0,n}function qe(e,t,s,n){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const c=r[o];i&&(c.oldValue=i[o].value);let u=c.dir[n];u&&(Re(),Te(u,s,8,[e.el,c,e,t]),Ie())}}const Ei=Symbol("_vte"),Oi=e=>e.__isTeleport;function Ks(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ks(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Ai(e,t){return I(e)?Q({name:e.name},t,{setup:e}):e}function nr(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function bt(e,t,s,n,r=!1){if(R(e)){e.forEach((D,F)=>bt(D,t&&(R(t)?t[F]:t),s,n,r));return}if(vt(n)&&!r){n.shapeFlag&512&&n.type.__asyncResolved&&n.component.subTree.component&&bt(e,t,s,n.component.subTree);return}const i=n.shapeFlag&4?Gs(n.component):n.el,o=r?null:i,{i:c,r:u}=e,h=t&&t.r,a=c.refs===V?c.refs={}:c.refs,p=c.setupState,v=N(p),x=p===V?()=>!1:D=>H(v,D);if(h!=null&&h!==u&&(J(h)?(a[h]=null,x(h)&&(p[h]=null)):Z(h)&&(h.value=null)),I(u))Ot(u,c,12,[o,a]);else{const D=J(u),F=Z(u);if(D||F){const Y=()=>{if(e.f){const $=D?x(u)?p[u]:a[u]:u.value;r?R($)&&Rs($,i):R($)?$.includes(i)||$.push(i):D?(a[u]=[i],x(u)&&(p[u]=a[u])):(u.value=[i],e.k&&(a[e.k]=u.value))}else D?(a[u]=o,x(u)&&(p[u]=o)):F&&(u.value=o,e.k&&(a[e.k]=o))};o?(Y.id=-1,ce(Y,s)):Y()}}}Xt().requestIdleCallback;Xt().cancelIdleCallback;const vt=e=>!!e.type.__asyncLoader,rr=e=>e.type.__isKeepAlive;function Pi(e,t){ir(e,"a",t)}function Ri(e,t){ir(e,"da",t)}function ir(e,t,s=ne){const n=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(kt(t,n,s),s){let r=s.parent;for(;r&&r.parent;)rr(r.parent.vnode)&&Ii(n,t,s,r),r=r.parent}}function Ii(e,t,s,n){const r=kt(t,e,n,!0);lr(()=>{Rs(n[t],r)},s)}function kt(e,t,s=ne,n=!1){if(s){const r=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Re();const c=At(s),u=Te(t,s,e,o);return c(),Ie(),u});return n?r.unshift(i):r.push(i),i}}const Me=e=>(t,s=ne)=>{(!Et||e==="sp")&&kt(e,(...n)=>t(...n),s)},Mi=Me("bm"),or=Me("m"),Fi=Me("bu"),Di=Me("u"),Ni=Me("bum"),lr=Me("um"),Hi=Me("sp"),Li=Me("rtg"),$i=Me("rtc");function ji(e,t=ne){kt("ec",e,t)}const Ui=Symbol.for("v-ndc"),xs=e=>e?Ar(e)?Gs(e):xs(e.parent):null,yt=Q(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>xs(e.parent),$root:e=>xs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>fr(e),$forceUpdate:e=>e.f||(e.f=()=>{Vs(e.update)}),$nextTick:e=>e.n||(e.n=Si.bind(e.proxy)),$watch:e=>co.bind(e)}),us=(e,t)=>e!==V&&!e.__isScriptSetup&&H(e,t),Bi={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:r,props:i,accessCache:o,type:c,appContext:u}=e;let h;if(t[0]!=="$"){const x=o[t];if(x!==void 0)switch(x){case 1:return n[t];case 2:return r[t];case 4:return s[t];case 3:return i[t]}else{if(us(n,t))return o[t]=1,n[t];if(r!==V&&H(r,t))return o[t]=2,r[t];if((h=e.propsOptions[0])&&H(h,t))return o[t]=3,i[t];if(s!==V&&H(s,t))return o[t]=4,s[t];Ss&&(o[t]=0)}}const a=yt[t];let p,v;if(a)return t==="$attrs"&&X(e.attrs,"get",""),a(e);if((p=c.__cssModules)&&(p=p[t]))return p;if(s!==V&&H(s,t))return o[t]=4,s[t];if(v=u.config.globalProperties,H(v,t))return v[t]},set({_:e},t,s){const{data:n,setupState:r,ctx:i}=e;return us(r,t)?(r[t]=s,!0):n!==V&&H(n,t)?(n[t]=s,!0):H(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:r,propsOptions:i}},o){let c;return!!s[o]||e!==V&&H(e,o)||us(t,o)||(c=i[0])&&H(c,o)||H(n,o)||H(yt,o)||H(r.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:H(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function rn(e){return R(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Ss=!0;function Vi(e){const t=fr(e),s=e.proxy,n=e.ctx;Ss=!1,t.beforeCreate&&on(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:c,provide:u,inject:h,created:a,beforeMount:p,mounted:v,beforeUpdate:x,updated:D,activated:F,deactivated:Y,beforeDestroy:$,beforeUnmount:W,destroyed:q,unmounted:O,render:G,renderTracked:Fe,renderTriggered:he,errorCaptured:De,serverPrefetch:Pt,expose:Ve,inheritAttrs:ot,components:Rt,directives:It,filters:ss}=t;if(h&&Ki(h,n,null),o)for(const K in o){const j=o[K];I(j)&&(n[K]=j.bind(s))}if(r){const K=r.call(s,s);z(K)&&(e.data=js(K))}if(Ss=!0,i)for(const K in i){const j=i[K],Ke=I(j)?j.bind(s,s):I(j.get)?j.get.bind(s,s):we,Mt=!I(j)&&I(j.set)?j.set.bind(s):we,We=Mo({get:Ke,set:Mt});Object.defineProperty(n,K,{enumerable:!0,configurable:!0,get:()=>We.value,set:pe=>We.value=pe})}if(c)for(const K in c)cr(c[K],n,s,K);if(u){const K=I(u)?u.call(s):u;Reflect.ownKeys(K).forEach(j=>{Yi(j,K[j])})}a&&on(a,e,"c");function k(K,j){R(j)?j.forEach(Ke=>K(Ke.bind(s))):j&&K(j.bind(s))}if(k(Mi,p),k(or,v),k(Fi,x),k(Di,D),k(Pi,F),k(Ri,Y),k(ji,De),k($i,Fe),k(Li,he),k(Ni,W),k(lr,O),k(Hi,Pt),R(Ve))if(Ve.length){const K=e.exposed||(e.exposed={});Ve.forEach(j=>{Object.defineProperty(K,j,{get:()=>s[j],set:Ke=>s[j]=Ke,enumerable:!0})})}else e.exposed||(e.exposed={});G&&e.render===we&&(e.render=G),ot!=null&&(e.inheritAttrs=ot),Rt&&(e.components=Rt),It&&(e.directives=It),Pt&&nr(e)}function Ki(e,t,s=we){R(e)&&(e=ws(e));for(const n in e){const r=e[n];let i;z(r)?"default"in r?i=$t(r.from||n,r.default,!0):i=$t(r.from||n):i=$t(r),Z(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[n]=i}}function on(e,t,s){Te(R(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function cr(e,t,s,n){let r=n.includes(".")?Sr(s,n):()=>s[n];if(J(e)){const i=t[e];I(i)&&ds(r,i)}else if(I(e))ds(r,e.bind(s));else if(z(e))if(R(e))e.forEach(i=>cr(i,t,s,n));else{const i=I(e.handler)?e.handler.bind(s):t[e.handler];I(i)&&ds(r,i,e)}}function fr(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,c=i.get(t);let u;return c?u=c:!r.length&&!s&&!n?u=t:(u={},r.length&&r.forEach(h=>qt(u,h,o,!0)),qt(u,t,o)),z(t)&&i.set(t,u),u}function qt(e,t,s,n=!1){const{mixins:r,extends:i}=t;i&&qt(e,i,s,!0),r&&r.forEach(o=>qt(e,o,s,!0));for(const o in t)if(!(n&&o==="expose")){const c=Wi[o]||s&&s[o];e[o]=c?c(e[o],t[o]):t[o]}return e}const Wi={data:ln,props:cn,emits:cn,methods:ht,computed:ht,beforeCreate:ee,created:ee,beforeMount:ee,mounted:ee,beforeUpdate:ee,updated:ee,beforeDestroy:ee,beforeUnmount:ee,destroyed:ee,unmounted:ee,activated:ee,deactivated:ee,errorCaptured:ee,serverPrefetch:ee,components:ht,directives:ht,watch:zi,provide:ln,inject:qi};function ln(e,t){return t?e?function(){return Q(I(e)?e.call(this,this):e,I(t)?t.call(this,this):t)}:t:e}function qi(e,t){return ht(ws(e),ws(t))}function ws(e){if(R(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function ee(e,t){return e?[...new Set([].concat(e,t))]:t}function ht(e,t){return e?Q(Object.create(null),e,t):t}function cn(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:Q(Object.create(null),rn(e),rn(t??{})):t}function zi(e,t){if(!e)return t;if(!t)return e;const s=Q(Object.create(null),e);for(const n in t)s[n]=ee(e[n],t[n]);return s}function ur(){return{app:null,config:{isNativeTag:Fr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Gi=0;function Ji(e,t){return function(n,r=null){I(n)||(n=Q({},n)),r!=null&&!z(r)&&(r=null);const i=ur(),o=new WeakSet,c=[];let u=!1;const h=i.app={_uid:Gi++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:Fo,get config(){return i.config},set config(a){},use(a,...p){return o.has(a)||(a&&I(a.install)?(o.add(a),a.install(h,...p)):I(a)&&(o.add(a),a(h,...p))),h},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),h},component(a,p){return p?(i.components[a]=p,h):i.components[a]},directive(a,p){return p?(i.directives[a]=p,h):i.directives[a]},mount(a,p,v){if(!u){const x=h._ceVNode||Ce(n,r);return x.appContext=i,v===!0?v="svg":v===!1&&(v=void 0),e(x,a,v),u=!0,h._container=a,a.__vue_app__=h,Gs(x.component)}},onUnmount(a){c.push(a)},unmount(){u&&(Te(c,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(a,p){return i.provides[a]=p,h},runWithContext(a){const p=rt;rt=h;try{return a()}finally{rt=p}}};return h}}let rt=null;function Yi(e,t){if(ne){let s=ne.provides;const n=ne.parent&&ne.parent.provides;n===s&&(s=ne.provides=Object.create(n)),s[e]=t}}function $t(e,t,s=!1){const n=Eo();if(n||rt){let r=rt?rt._context.provides:n?n.parent==null||n.ce?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&I(t)?t.call(n&&n.proxy):t}}const ar={},dr=()=>Object.create(ar),hr=e=>Object.getPrototypeOf(e)===ar;function Xi(e,t,s,n=!1){const r={},i=dr();e.propsDefaults=Object.create(null),pr(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);s?e.props=n?r:ai(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function Zi(e,t,s,n){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,c=N(r),[u]=e.propsOptions;let h=!1;if((n||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let p=0;p<a.length;p++){let v=a[p];if(es(e.emitsOptions,v))continue;const x=t[v];if(u)if(H(i,v))x!==i[v]&&(i[v]=x,h=!0);else{const D=je(v);r[D]=Cs(u,c,D,x,e,!1)}else x!==i[v]&&(i[v]=x,h=!0)}}}else{pr(e,t,r,i)&&(h=!0);let a;for(const p in c)(!t||!H(t,p)&&((a=Ze(p))===p||!H(t,a)))&&(u?s&&(s[p]!==void 0||s[a]!==void 0)&&(r[p]=Cs(u,c,p,void 0,e,!0)):delete r[p]);if(i!==c)for(const p in i)(!t||!H(t,p))&&(delete i[p],h=!0)}h&&Pe(e.attrs,"set","")}function pr(e,t,s,n){const[r,i]=e.propsOptions;let o=!1,c;if(t)for(let u in t){if(pt(u))continue;const h=t[u];let a;r&&H(r,a=je(u))?!i||!i.includes(a)?s[a]=h:(c||(c={}))[a]=h:es(e.emitsOptions,u)||(!(u in n)||h!==n[u])&&(n[u]=h,o=!0)}if(i){const u=N(s),h=c||V;for(let a=0;a<i.length;a++){const p=i[a];s[p]=Cs(r,u,p,h[p],e,!H(h,p))}}return o}function Cs(e,t,s,n,r,i){const o=e[s];if(o!=null){const c=H(o,"default");if(c&&n===void 0){const u=o.default;if(o.type!==Function&&!o.skipFactory&&I(u)){const{propsDefaults:h}=r;if(s in h)n=h[s];else{const a=At(r);n=h[s]=u.call(null,t),a()}}else n=u;r.ce&&r.ce._setProp(s,n)}o[0]&&(i&&!c?n=!1:o[1]&&(n===""||n===Ze(s))&&(n=!0))}return n}const Qi=new WeakMap;function gr(e,t,s=!1){const n=s?Qi:t.propsCache,r=n.get(e);if(r)return r;const i=e.props,o={},c=[];let u=!1;if(!I(e)){const a=p=>{u=!0;const[v,x]=gr(p,t,!0);Q(o,v),x&&c.push(...x)};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!u)return z(e)&&n.set(e,tt),tt;if(R(i))for(let a=0;a<i.length;a++){const p=je(i[a]);fn(p)&&(o[p]=V)}else if(i)for(const a in i){const p=je(a);if(fn(p)){const v=i[a],x=o[p]=R(v)||I(v)?{type:v}:Q({},v),D=x.type;let F=!1,Y=!0;if(R(D))for(let $=0;$<D.length;++$){const W=D[$],q=I(W)&&W.name;if(q==="Boolean"){F=!0;break}else q==="String"&&(Y=!1)}else F=I(D)&&D.name==="Boolean";x[0]=F,x[1]=Y,(F||H(x,"default"))&&c.push(p)}}const h=[o,c];return z(e)&&n.set(e,h),h}function fn(e){return e[0]!=="$"&&!pt(e)}const Ws=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",qs=e=>R(e)?e.map(xe):[xe(e)],ki=(e,t,s)=>{if(t._n)return t;const n=Ti((...r)=>qs(t(...r)),s);return n._c=!1,n},_r=(e,t,s)=>{const n=e._ctx;for(const r in e){if(Ws(r))continue;const i=e[r];if(I(i))t[r]=ki(r,i,n);else if(i!=null){const o=qs(i);t[r]=()=>o}}},mr=(e,t)=>{const s=qs(t);e.slots.default=()=>s},br=(e,t,s)=>{for(const n in t)(s||!Ws(n))&&(e[n]=t[n])},eo=(e,t,s)=>{const n=e.slots=dr();if(e.vnode.shapeFlag&32){const r=t.__;r&&gs(n,"__",r,!0);const i=t._;i?(br(n,t,s),s&&gs(n,"_",i,!0)):_r(t,n)}else t&&mr(e,t)},to=(e,t,s)=>{const{vnode:n,slots:r}=e;let i=!0,o=V;if(n.shapeFlag&32){const c=t._;c?s&&c===1?i=!1:br(r,t,s):(i=!t.$stable,_r(t,r)),o=t}else t&&(mr(e,t),o={default:1});if(i)for(const c in r)!Ws(c)&&o[c]==null&&delete r[c]},ce=_o;function so(e){return no(e)}function no(e,t){const s=Xt();s.__VUE__=!0;const{insert:n,remove:r,patchProp:i,createElement:o,createText:c,createComment:u,setText:h,setElementText:a,parentNode:p,nextSibling:v,setScopeId:x=we,insertStaticContent:D}=e,F=(l,f,d,m=null,g=null,_=null,w=void 0,S=null,y=!!f.dynamicChildren)=>{if(l===f)return;l&&!dt(l,f)&&(m=Ft(l),pe(l,g,_,!0),l=null),f.patchFlag===-2&&(y=!1,f.dynamicChildren=null);const{type:b,ref:E,shapeFlag:C}=f;switch(b){case ts:Y(l,f,d,m);break;case Ue:$(l,f,d,m);break;case jt:l==null&&W(f,d,m,w);break;case Ae:Rt(l,f,d,m,g,_,w,S,y);break;default:C&1?G(l,f,d,m,g,_,w,S,y):C&6?It(l,f,d,m,g,_,w,S,y):(C&64||C&128)&&b.process(l,f,d,m,g,_,w,S,y,ct)}E!=null&&g?bt(E,l&&l.ref,_,f||l,!f):E==null&&l&&l.ref!=null&&bt(l.ref,null,_,l,!0)},Y=(l,f,d,m)=>{if(l==null)n(f.el=c(f.children),d,m);else{const g=f.el=l.el;f.children!==l.children&&h(g,f.children)}},$=(l,f,d,m)=>{l==null?n(f.el=u(f.children||""),d,m):f.el=l.el},W=(l,f,d,m)=>{[l.el,l.anchor]=D(l.children,f,d,m,l.el,l.anchor)},q=({el:l,anchor:f},d,m)=>{let g;for(;l&&l!==f;)g=v(l),n(l,d,m),l=g;n(f,d,m)},O=({el:l,anchor:f})=>{let d;for(;l&&l!==f;)d=v(l),r(l),l=d;r(f)},G=(l,f,d,m,g,_,w,S,y)=>{f.type==="svg"?w="svg":f.type==="math"&&(w="mathml"),l==null?Fe(f,d,m,g,_,w,S,y):Pt(l,f,g,_,w,S,y)},Fe=(l,f,d,m,g,_,w,S)=>{let y,b;const{props:E,shapeFlag:C,transition:T,dirs:A}=l;if(y=l.el=o(l.type,_,E&&E.is,E),C&8?a(y,l.children):C&16&&De(l.children,y,null,m,g,as(l,_),w,S),A&&qe(l,null,m,"created"),he(y,l,l.scopeId,w,m),E){for(const U in E)U!=="value"&&!pt(U)&&i(y,U,null,E[U],_,m);"value"in E&&i(y,"value",null,E.value,_),(b=E.onVnodeBeforeMount)&&ve(b,m,l)}A&&qe(l,null,m,"beforeMount");const M=ro(g,T);M&&T.beforeEnter(y),n(y,f,d),((b=E&&E.onVnodeMounted)||M||A)&&ce(()=>{b&&ve(b,m,l),M&&T.enter(y),A&&qe(l,null,m,"mounted")},g)},he=(l,f,d,m,g)=>{if(d&&x(l,d),m)for(let _=0;_<m.length;_++)x(l,m[_]);if(g){let _=g.subTree;if(f===_||Cr(_.type)&&(_.ssContent===f||_.ssFallback===f)){const w=g.vnode;he(l,w,w.scopeId,w.slotScopeIds,g.parent)}}},De=(l,f,d,m,g,_,w,S,y=0)=>{for(let b=y;b<l.length;b++){const E=l[b]=S?He(l[b]):xe(l[b]);F(null,E,f,d,m,g,_,w,S)}},Pt=(l,f,d,m,g,_,w)=>{const S=f.el=l.el;let{patchFlag:y,dynamicChildren:b,dirs:E}=f;y|=l.patchFlag&16;const C=l.props||V,T=f.props||V;let A;if(d&&ze(d,!1),(A=T.onVnodeBeforeUpdate)&&ve(A,d,f,l),E&&qe(f,l,d,"beforeUpdate"),d&&ze(d,!0),(C.innerHTML&&T.innerHTML==null||C.textContent&&T.textContent==null)&&a(S,""),b?Ve(l.dynamicChildren,b,S,d,m,as(f,g),_):w||j(l,f,S,null,d,m,as(f,g),_,!1),y>0){if(y&16)ot(S,C,T,d,g);else if(y&2&&C.class!==T.class&&i(S,"class",null,T.class,g),y&4&&i(S,"style",C.style,T.style,g),y&8){const M=f.dynamicProps;for(let U=0;U<M.length;U++){const L=M[U],re=C[L],ie=T[L];(ie!==re||L==="value")&&i(S,L,re,ie,g,d)}}y&1&&l.children!==f.children&&a(S,f.children)}else!w&&b==null&&ot(S,C,T,d,g);((A=T.onVnodeUpdated)||E)&&ce(()=>{A&&ve(A,d,f,l),E&&qe(f,l,d,"updated")},m)},Ve=(l,f,d,m,g,_,w)=>{for(let S=0;S<f.length;S++){const y=l[S],b=f[S],E=y.el&&(y.type===Ae||!dt(y,b)||y.shapeFlag&198)?p(y.el):d;F(y,b,E,null,m,g,_,w,!0)}},ot=(l,f,d,m,g)=>{if(f!==d){if(f!==V)for(const _ in f)!pt(_)&&!(_ in d)&&i(l,_,f[_],null,g,m);for(const _ in d){if(pt(_))continue;const w=d[_],S=f[_];w!==S&&_!=="value"&&i(l,_,S,w,g,m)}"value"in d&&i(l,"value",f.value,d.value,g)}},Rt=(l,f,d,m,g,_,w,S,y)=>{const b=f.el=l?l.el:c(""),E=f.anchor=l?l.anchor:c("");let{patchFlag:C,dynamicChildren:T,slotScopeIds:A}=f;A&&(S=S?S.concat(A):A),l==null?(n(b,d,m),n(E,d,m),De(f.children||[],d,E,g,_,w,S,y)):C>0&&C&64&&T&&l.dynamicChildren?(Ve(l.dynamicChildren,T,d,g,_,w,S),(f.key!=null||g&&f===g.subTree)&&vr(l,f,!0)):j(l,f,d,E,g,_,w,S,y)},It=(l,f,d,m,g,_,w,S,y)=>{f.slotScopeIds=S,l==null?f.shapeFlag&512?g.ctx.activate(f,d,m,w,y):ss(f,d,m,g,_,w,y):Js(l,f,y)},ss=(l,f,d,m,g,_,w)=>{const S=l.component=To(l,m,g);if(rr(l)&&(S.ctx.renderer=ct),Oo(S,!1,w),S.asyncDep){if(g&&g.registerDep(S,k,w),!l.el){const y=S.subTree=Ce(Ue);$(null,y,f,d),l.placeholder=y.el}}else k(S,l,f,d,g,_,w)},Js=(l,f,d)=>{const m=f.component=l.component;if(po(l,f,d))if(m.asyncDep&&!m.asyncResolved){K(m,f,d);return}else m.next=f,m.update();else f.el=l.el,m.vnode=f},k=(l,f,d,m,g,_,w)=>{const S=()=>{if(l.isMounted){let{next:C,bu:T,u:A,parent:M,vnode:U}=l;{const _e=yr(l);if(_e){C&&(C.el=U.el,K(l,C,w)),_e.asyncDep.then(()=>{l.isUnmounted||S()});return}}let L=C,re;ze(l,!1),C?(C.el=U.el,K(l,C,w)):C=U,T&&is(T),(re=C.props&&C.props.onVnodeBeforeUpdate)&&ve(re,M,C,U),ze(l,!0);const ie=an(l),ge=l.subTree;l.subTree=ie,F(ge,ie,p(ge.el),Ft(ge),l,g,_),C.el=ie.el,L===null&&go(l,ie.el),A&&ce(A,g),(re=C.props&&C.props.onVnodeUpdated)&&ce(()=>ve(re,M,C,U),g)}else{let C;const{el:T,props:A}=f,{bm:M,m:U,parent:L,root:re,type:ie}=l,ge=vt(f);ze(l,!1),M&&is(M),!ge&&(C=A&&A.onVnodeBeforeMount)&&ve(C,L,f),ze(l,!0);{re.ce&&re.ce._def.shadowRoot!==!1&&re.ce._injectChildStyle(ie);const _e=l.subTree=an(l);F(null,_e,d,m,l,g,_),f.el=_e.el}if(U&&ce(U,g),!ge&&(C=A&&A.onVnodeMounted)){const _e=f;ce(()=>ve(C,L,_e),g)}(f.shapeFlag&256||L&&vt(L.vnode)&&L.vnode.shapeFlag&256)&&l.a&&ce(l.a,g),l.isMounted=!0,f=d=m=null}};l.scope.on();const y=l.effect=new Hn(S);l.scope.off();const b=l.update=y.run.bind(y),E=l.job=y.runIfDirty.bind(y);E.i=l,E.id=l.uid,y.scheduler=()=>Vs(E),ze(l,!0),b()},K=(l,f,d)=>{f.component=l;const m=l.vnode.props;l.vnode=f,l.next=null,Zi(l,f.props,m,d),to(l,f.children,d),Re(),nn(l),Ie()},j=(l,f,d,m,g,_,w,S,y=!1)=>{const b=l&&l.children,E=l?l.shapeFlag:0,C=f.children,{patchFlag:T,shapeFlag:A}=f;if(T>0){if(T&128){Mt(b,C,d,m,g,_,w,S,y);return}else if(T&256){Ke(b,C,d,m,g,_,w,S,y);return}}A&8?(E&16&&lt(b,g,_),C!==b&&a(d,C)):E&16?A&16?Mt(b,C,d,m,g,_,w,S,y):lt(b,g,_,!0):(E&8&&a(d,""),A&16&&De(C,d,m,g,_,w,S,y))},Ke=(l,f,d,m,g,_,w,S,y)=>{l=l||tt,f=f||tt;const b=l.length,E=f.length,C=Math.min(b,E);let T;for(T=0;T<C;T++){const A=f[T]=y?He(f[T]):xe(f[T]);F(l[T],A,d,null,g,_,w,S,y)}b>E?lt(l,g,_,!0,!1,C):De(f,d,m,g,_,w,S,y,C)},Mt=(l,f,d,m,g,_,w,S,y)=>{let b=0;const E=f.length;let C=l.length-1,T=E-1;for(;b<=C&&b<=T;){const A=l[b],M=f[b]=y?He(f[b]):xe(f[b]);if(dt(A,M))F(A,M,d,null,g,_,w,S,y);else break;b++}for(;b<=C&&b<=T;){const A=l[C],M=f[T]=y?He(f[T]):xe(f[T]);if(dt(A,M))F(A,M,d,null,g,_,w,S,y);else break;C--,T--}if(b>C){if(b<=T){const A=T+1,M=A<E?f[A].el:m;for(;b<=T;)F(null,f[b]=y?He(f[b]):xe(f[b]),d,M,g,_,w,S,y),b++}}else if(b>T)for(;b<=C;)pe(l[b],g,_,!0),b++;else{const A=b,M=b,U=new Map;for(b=M;b<=T;b++){const le=f[b]=y?He(f[b]):xe(f[b]);le.key!=null&&U.set(le.key,b)}let L,re=0;const ie=T-M+1;let ge=!1,_e=0;const ft=new Array(ie);for(b=0;b<ie;b++)ft[b]=0;for(b=A;b<=C;b++){const le=l[b];if(re>=ie){pe(le,g,_,!0);continue}let me;if(le.key!=null)me=U.get(le.key);else for(L=M;L<=T;L++)if(ft[L-M]===0&&dt(le,f[L])){me=L;break}me===void 0?pe(le,g,_,!0):(ft[me-M]=b+1,me>=_e?_e=me:ge=!0,F(le,f[me],d,null,g,_,w,S,y),re++)}const Zs=ge?io(ft):tt;for(L=Zs.length-1,b=ie-1;b>=0;b--){const le=M+b,me=f[le],Qs=f[le+1],ks=le+1<E?Qs.el||Qs.placeholder:m;ft[b]===0?F(null,me,d,ks,g,_,w,S,y):ge&&(L<0||b!==Zs[L]?We(me,d,ks,2):L--)}}},We=(l,f,d,m,g=null)=>{const{el:_,type:w,transition:S,children:y,shapeFlag:b}=l;if(b&6){We(l.component.subTree,f,d,m);return}if(b&128){l.suspense.move(f,d,m);return}if(b&64){w.move(l,f,d,ct);return}if(w===Ae){n(_,f,d);for(let C=0;C<y.length;C++)We(y[C],f,d,m);n(l.anchor,f,d);return}if(w===jt){q(l,f,d);return}if(m!==2&&b&1&&S)if(m===0)S.beforeEnter(_),n(_,f,d),ce(()=>S.enter(_),g);else{const{leave:C,delayLeave:T,afterLeave:A}=S,M=()=>{l.ctx.isUnmounted?r(_):n(_,f,d)},U=()=>{C(_,()=>{M(),A&&A()})};T?T(_,M,U):U()}else n(_,f,d)},pe=(l,f,d,m=!1,g=!1)=>{const{type:_,props:w,ref:S,children:y,dynamicChildren:b,shapeFlag:E,patchFlag:C,dirs:T,cacheIndex:A}=l;if(C===-2&&(g=!1),S!=null&&(Re(),bt(S,null,d,l,!0),Ie()),A!=null&&(f.renderCache[A]=void 0),E&256){f.ctx.deactivate(l);return}const M=E&1&&T,U=!vt(l);let L;if(U&&(L=w&&w.onVnodeBeforeUnmount)&&ve(L,f,l),E&6)Mr(l.component,d,m);else{if(E&128){l.suspense.unmount(d,m);return}M&&qe(l,null,f,"beforeUnmount"),E&64?l.type.remove(l,f,d,ct,m):b&&!b.hasOnce&&(_!==Ae||C>0&&C&64)?lt(b,f,d,!1,!0):(_===Ae&&C&384||!g&&E&16)&&lt(y,f,d),m&&Ys(l)}(U&&(L=w&&w.onVnodeUnmounted)||M)&&ce(()=>{L&&ve(L,f,l),M&&qe(l,null,f,"unmounted")},d)},Ys=l=>{const{type:f,el:d,anchor:m,transition:g}=l;if(f===Ae){Ir(d,m);return}if(f===jt){O(l);return}const _=()=>{r(d),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(l.shapeFlag&1&&g&&!g.persisted){const{leave:w,delayLeave:S}=g,y=()=>w(d,_);S?S(l.el,_,y):y()}else _()},Ir=(l,f)=>{let d;for(;l!==f;)d=v(l),r(l),l=d;r(f)},Mr=(l,f,d)=>{const{bum:m,scope:g,job:_,subTree:w,um:S,m:y,a:b,parent:E,slots:{__:C}}=l;un(y),un(b),m&&is(m),E&&R(C)&&C.forEach(T=>{E.renderCache[T]=void 0}),g.stop(),_&&(_.flags|=8,pe(w,l,f,d)),S&&ce(S,f),ce(()=>{l.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&l.asyncDep&&!l.asyncResolved&&l.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},lt=(l,f,d,m=!1,g=!1,_=0)=>{for(let w=_;w<l.length;w++)pe(l[w],f,d,m,g)},Ft=l=>{if(l.shapeFlag&6)return Ft(l.component.subTree);if(l.shapeFlag&128)return l.suspense.next();const f=v(l.anchor||l.el),d=f&&f[Ei];return d?v(d):f};let ns=!1;const Xs=(l,f,d)=>{l==null?f._vnode&&pe(f._vnode,null,null,!0):F(f._vnode||null,l,f,null,null,null,d),f._vnode=l,ns||(ns=!0,nn(),er(),ns=!1)},ct={p:F,um:pe,m:We,r:Ys,mt:ss,mc:De,pc:j,pbc:Ve,n:Ft,o:e};return{render:Xs,hydrate:void 0,createApp:Ji(Xs)}}function as({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function ze({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ro(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function vr(e,t,s=!1){const n=e.children,r=t.children;if(R(n)&&R(r))for(let i=0;i<n.length;i++){const o=n[i];let c=r[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[i]=He(r[i]),c.el=o.el),!s&&c.patchFlag!==-2&&vr(o,c)),c.type===ts&&(c.el=o.el),c.type===Ue&&!c.el&&(c.el=o.el)}}function io(e){const t=e.slice(),s=[0];let n,r,i,o,c;const u=e.length;for(n=0;n<u;n++){const h=e[n];if(h!==0){if(r=s[s.length-1],e[r]<h){t[n]=r,s.push(n);continue}for(i=0,o=s.length-1;i<o;)c=i+o>>1,e[s[c]]<h?i=c+1:o=c;h<e[s[i]]&&(i>0&&(t[n]=s[i-1]),s[i]=n)}}for(i=s.length,o=s[i-1];i-- >0;)s[i]=o,o=t[o];return s}function yr(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:yr(t)}function un(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const oo=Symbol.for("v-scx"),lo=()=>$t(oo);function ds(e,t,s){return xr(e,t,s)}function xr(e,t,s=V){const{immediate:n,deep:r,flush:i,once:o}=s,c=Q({},s),u=t&&n||!t&&i!=="post";let h;if(Et){if(i==="sync"){const x=lo();h=x.__watcherHandles||(x.__watcherHandles=[])}else if(!u){const x=()=>{};return x.stop=we,x.resume=we,x.pause=we,x}}const a=ne;c.call=(x,D,F)=>Te(x,a,D,F);let p=!1;i==="post"?c.scheduler=x=>{ce(x,a&&a.suspense)}:i!=="sync"&&(p=!0,c.scheduler=(x,D)=>{D?x():Vs(x)}),c.augmentJob=x=>{t&&(x.flags|=4),p&&(x.flags|=2,a&&(x.id=a.uid,x.i=a))};const v=yi(e,t,c);return Et&&(h?h.push(v):u&&v()),v}function co(e,t,s){const n=this.proxy,r=J(e)?e.includes(".")?Sr(n,e):()=>n[e]:e.bind(n,n);let i;I(t)?i=t:(i=t.handler,s=t);const o=At(this),c=xr(r,i.bind(n),s);return o(),c}function Sr(e,t){const s=t.split(".");return()=>{let n=e;for(let r=0;r<s.length&&n;r++)n=n[s[r]];return n}}const fo=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${je(t)}Modifiers`]||e[`${Ze(t)}Modifiers`];function uo(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||V;let r=s;const i=t.startsWith("update:"),o=i&&fo(n,t.slice(7));o&&(o.trim&&(r=s.map(a=>J(a)?a.trim():a)),o.number&&(r=s.map($r)));let c,u=n[c=rs(t)]||n[c=rs(je(t))];!u&&i&&(u=n[c=rs(Ze(t))]),u&&Te(u,e,6,r);const h=n[c+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Te(h,e,6,r)}}function wr(e,t,s=!1){const n=t.emitsCache,r=n.get(e);if(r!==void 0)return r;const i=e.emits;let o={},c=!1;if(!I(e)){const u=h=>{const a=wr(h,t,!0);a&&(c=!0,Q(o,a))};!s&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}return!i&&!c?(z(e)&&n.set(e,null),null):(R(i)?i.forEach(u=>o[u]=null):Q(o,i),z(e)&&n.set(e,o),o)}function es(e,t){return!e||!Gt(t)?!1:(t=t.slice(2).replace(/Once$/,""),H(e,t[0].toLowerCase()+t.slice(1))||H(e,Ze(t))||H(e,t))}function an(e){const{type:t,vnode:s,proxy:n,withProxy:r,propsOptions:[i],slots:o,attrs:c,emit:u,render:h,renderCache:a,props:p,data:v,setupState:x,ctx:D,inheritAttrs:F}=e,Y=Wt(e);let $,W;try{if(s.shapeFlag&4){const O=r||n,G=O;$=xe(h.call(G,O,a,p,x,v,D)),W=c}else{const O=t;$=xe(O.length>1?O(p,{attrs:c,slots:o,emit:u}):O(p,null)),W=t.props?c:ao(c)}}catch(O){xt.length=0,Qt(O,e,1),$=Ce(Ue)}let q=$;if(W&&F!==!1){const O=Object.keys(W),{shapeFlag:G}=q;O.length&&G&7&&(i&&O.some(Ps)&&(W=ho(W,i)),q=it(q,W,!1,!0))}return s.dirs&&(q=it(q,null,!1,!0),q.dirs=q.dirs?q.dirs.concat(s.dirs):s.dirs),s.transition&&Ks(q,s.transition),$=q,Wt(Y),$}const ao=e=>{let t;for(const s in e)(s==="class"||s==="style"||Gt(s))&&((t||(t={}))[s]=e[s]);return t},ho=(e,t)=>{const s={};for(const n in e)(!Ps(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function po(e,t,s){const{props:n,children:r,component:i}=e,{props:o,children:c,patchFlag:u}=t,h=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&u>=0){if(u&1024)return!0;if(u&16)return n?dn(n,o,h):!!o;if(u&8){const a=t.dynamicProps;for(let p=0;p<a.length;p++){const v=a[p];if(o[v]!==n[v]&&!es(h,v))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:n===o?!1:n?o?dn(n,o,h):!0:!!o;return!1}function dn(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let r=0;r<n.length;r++){const i=n[r];if(t[i]!==e[i]&&!es(s,i))return!0}return!1}function go({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const Cr=e=>e.__isSuspense;function _o(e,t){t&&t.pendingBranch?R(e)?t.effects.push(...e):t.effects.push(e):Ci(e)}const Ae=Symbol.for("v-fgt"),ts=Symbol.for("v-txt"),Ue=Symbol.for("v-cmt"),jt=Symbol.for("v-stc"),xt=[];let fe=null;function ue(e=!1){xt.push(fe=e?null:[])}function mo(){xt.pop(),fe=xt[xt.length-1]||null}let Tt=1;function hn(e,t=!1){Tt+=e,e<0&&fe&&t&&(fe.hasOnce=!0)}function Tr(e){return e.dynamicChildren=Tt>0?fe||tt:null,mo(),Tt>0&&fe&&fe.push(e),e}function be(e,t,s,n,r,i){return Tr(P(e,t,s,n,r,i,!0))}function bo(e,t,s,n,r){return Tr(Ce(e,t,s,n,r,!0))}function Er(e){return e?e.__v_isVNode===!0:!1}function dt(e,t){return e.type===t.type&&e.key===t.key}const Or=({key:e})=>e??null,Ut=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?J(e)||Z(e)||I(e)?{i:Se,r:e,k:t,f:!!s}:e:null);function P(e,t=null,s=null,n=0,r=null,i=e===Ae?0:1,o=!1,c=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Or(t),ref:t&&Ut(t),scopeId:sr,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:n,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Se};return c?(zs(u,s),i&128&&e.normalize(u)):s&&(u.shapeFlag|=J(s)?8:16),Tt>0&&!o&&fe&&(u.patchFlag>0||i&6)&&u.patchFlag!==32&&fe.push(u),u}const Ce=vo;function vo(e,t=null,s=null,n=0,r=null,i=!1){if((!e||e===Ui)&&(e=Ue),Er(e)){const c=it(e,t,!0);return s&&zs(c,s),Tt>0&&!i&&fe&&(c.shapeFlag&6?fe[fe.indexOf(e)]=c:fe.push(c)),c.patchFlag=-2,c}if(Io(e)&&(e=e.__vccOpts),t){t=yo(t);let{class:c,style:u}=t;c&&!J(c)&&(t.class=Ms(c)),z(u)&&(Bs(u)&&!R(u)&&(u=Q({},u)),t.style=Zt(u))}const o=J(e)?1:Cr(e)?128:Oi(e)?64:z(e)?4:I(e)?2:0;return P(e,t,s,n,r,o,i,!0)}function yo(e){return e?Bs(e)||hr(e)?Q({},e):e:null}function it(e,t,s=!1,n=!1){const{props:r,ref:i,patchFlag:o,children:c,transition:u}=e,h=t?So(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Or(h),ref:t&&t.ref?s&&i?R(i)?i.concat(Ut(t)):[i,Ut(t)]:Ut(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ae?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:u,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&it(e.ssContent),ssFallback:e.ssFallback&&it(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u&&n&&Ks(a,u.clone(a)),a}function Ts(e=" ",t=0){return Ce(ts,null,e,t)}function xo(e,t){const s=Ce(jt,null,e);return s.staticCount=t,s}function ke(e="",t=!1){return t?(ue(),bo(Ue,null,e)):Ce(Ue,null,e)}function xe(e){return e==null||typeof e=="boolean"?Ce(Ue):R(e)?Ce(Ae,null,e.slice()):Er(e)?He(e):Ce(ts,null,String(e))}function He(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:it(e)}function zs(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(R(t))s=16;else if(typeof t=="object")if(n&65){const r=t.default;r&&(r._c&&(r._d=!1),zs(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!hr(t)?t._ctx=Se:r===3&&Se&&(Se.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else I(t)?(t={default:t,_ctx:Se},s=32):(t=String(t),n&64?(s=16,t=[Ts(t)]):s=8);e.children=t,e.shapeFlag|=s}function So(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const r in n)if(r==="class")t.class!==n.class&&(t.class=Ms([t.class,n.class]));else if(r==="style")t.style=Zt([t.style,n.style]);else if(Gt(r)){const i=t[r],o=n[r];o&&i!==o&&!(R(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=n[r])}return t}function ve(e,t,s,n=null){Te(e,t,7,[s,n])}const wo=ur();let Co=0;function To(e,t,s){const n=e.type,r=(t?t.appContext:e.appContext)||wo,i={uid:Co++,vnode:e,type:n,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new qr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:gr(n,r),emitsOptions:wr(n,r),emit:null,emitted:null,propsDefaults:V,inheritAttrs:n.inheritAttrs,ctx:V,data:V,props:V,attrs:V,slots:V,refs:V,setupState:V,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=uo.bind(null,i),e.ce&&e.ce(i),i}let ne=null;const Eo=()=>ne||Se;let zt,Es;{const e=Xt(),t=(s,n)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(n),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};zt=t("__VUE_INSTANCE_SETTERS__",s=>ne=s),Es=t("__VUE_SSR_SETTERS__",s=>Et=s)}const At=e=>{const t=ne;return zt(e),e.scope.on(),()=>{e.scope.off(),zt(t)}},pn=()=>{ne&&ne.scope.off(),zt(null)};function Ar(e){return e.vnode.shapeFlag&4}let Et=!1;function Oo(e,t=!1,s=!1){t&&Es(t);const{props:n,children:r}=e.vnode,i=Ar(e);Xi(e,n,i,t),eo(e,r,s||t);const o=i?Ao(e,t):void 0;return t&&Es(!1),o}function Ao(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Bi);const{setup:n}=s;if(n){Re();const r=e.setupContext=n.length>1?Ro(e):null,i=At(e),o=Ot(n,e,0,[e.props,r]),c=Pn(o);if(Ie(),i(),(c||e.sp)&&!vt(e)&&nr(e),c){if(o.then(pn,pn),t)return o.then(u=>{gn(e,u)}).catch(u=>{Qt(u,e,0)});e.asyncDep=o}else gn(e,o)}else Pr(e)}function gn(e,t,s){I(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:z(t)&&(e.setupState=Zn(t)),Pr(e)}function Pr(e,t,s){const n=e.type;e.render||(e.render=n.render||we);{const r=At(e);Re();try{Vi(e)}finally{Ie(),r()}}}const Po={get(e,t){return X(e,"get",""),e[t]}};function Ro(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Po),slots:e.slots,emit:e.emit,expose:t}}function Gs(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Zn(di(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in yt)return yt[s](e)},has(t,s){return s in t||s in yt}})):e.proxy}function Io(e){return I(e)&&"__vccOpts"in e}const Mo=(e,t)=>bi(e,t,Et),Fo="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Os;const _n=typeof window<"u"&&window.trustedTypes;if(_n)try{Os=_n.createPolicy("vue",{createHTML:e=>e})}catch{}const Rr=Os?e=>Os.createHTML(e):e=>e,Do="http://www.w3.org/2000/svg",No="http://www.w3.org/1998/Math/MathML",Oe=typeof document<"u"?document:null,mn=Oe&&Oe.createElement("template"),Ho={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const r=t==="svg"?Oe.createElementNS(Do,e):t==="mathml"?Oe.createElementNS(No,e):s?Oe.createElement(e,{is:s}):Oe.createElement(e);return e==="select"&&n&&n.multiple!=null&&r.setAttribute("multiple",n.multiple),r},createText:e=>Oe.createTextNode(e),createComment:e=>Oe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Oe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,r,i){const o=s?s.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===i||!(r=r.nextSibling)););else{mn.innerHTML=Rr(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const c=mn.content;if(n==="svg"||n==="mathml"){const u=c.firstChild;for(;u.firstChild;)c.appendChild(u.firstChild);c.removeChild(u)}t.insertBefore(c,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Lo=Symbol("_vtc");function $o(e,t,s){const n=e[Lo];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const bn=Symbol("_vod"),jo=Symbol("_vsh"),Uo=Symbol(""),Bo=/(^|;)\s*display\s*:/;function Vo(e,t,s){const n=e.style,r=J(s);let i=!1;if(s&&!r){if(t)if(J(t))for(const o of t.split(";")){const c=o.slice(0,o.indexOf(":")).trim();s[c]==null&&Bt(n,c,"")}else for(const o in t)s[o]==null&&Bt(n,o,"");for(const o in s)o==="display"&&(i=!0),Bt(n,o,s[o])}else if(r){if(t!==s){const o=n[Uo];o&&(s+=";"+o),n.cssText=s,i=Bo.test(s)}}else t&&e.removeAttribute("style");bn in e&&(e[bn]=i?n.display:"",e[jo]&&(n.display="none"))}const vn=/\s*!important$/;function Bt(e,t,s){if(R(s))s.forEach(n=>Bt(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Ko(e,t);vn.test(s)?e.setProperty(Ze(n),s.replace(vn,""),"important"):e[n]=s}}const yn=["Webkit","Moz","ms"],hs={};function Ko(e,t){const s=hs[t];if(s)return s;let n=je(t);if(n!=="filter"&&n in e)return hs[t]=n;n=Mn(n);for(let r=0;r<yn.length;r++){const i=yn[r]+n;if(i in e)return hs[t]=i}return t}const xn="http://www.w3.org/1999/xlink";function Sn(e,t,s,n,r,i=Wr(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(xn,t.slice(6,t.length)):e.setAttributeNS(xn,t,s):s==null||i&&!Fn(s)?e.removeAttribute(t):e.setAttribute(t,i?"":Be(s)?String(s):s)}function wn(e,t,s,n,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Rr(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,u=s==null?e.type==="checkbox"?"on":"":String(s);(c!==u||!("_value"in e))&&(e.value=u),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Fn(s):s==null&&c==="string"?(s="",o=!0):c==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(r||t)}function Wo(e,t,s,n){e.addEventListener(t,s,n)}function qo(e,t,s,n){e.removeEventListener(t,s,n)}const Cn=Symbol("_vei");function zo(e,t,s,n,r=null){const i=e[Cn]||(e[Cn]={}),o=i[t];if(n&&o)o.value=n;else{const[c,u]=Go(t);if(n){const h=i[t]=Xo(n,r);Wo(e,c,h,u)}else o&&(qo(e,c,o,u),i[t]=void 0)}}const Tn=/(?:Once|Passive|Capture)$/;function Go(e){let t;if(Tn.test(e)){t={};let n;for(;n=e.match(Tn);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Ze(e.slice(2)),t]}let ps=0;const Jo=Promise.resolve(),Yo=()=>ps||(Jo.then(()=>ps=0),ps=Date.now());function Xo(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;Te(Zo(n,s.value),t,5,[n])};return s.value=e,s.attached=Yo(),s}function Zo(e,t){if(R(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>r=>!r._stopped&&n&&n(r))}else return t}const En=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Qo=(e,t,s,n,r,i)=>{const o=r==="svg";t==="class"?$o(e,n,o):t==="style"?Vo(e,s,n):Gt(t)?Ps(t)||zo(e,t,s,n,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ko(e,t,n,o))?(wn(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Sn(e,t,n,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!J(n))?wn(e,je(t),n,i,t):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Sn(e,t,n,o))};function ko(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&En(t)&&I(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return En(t)&&J(s)?!1:t in e}const el=Q({patchProp:Qo},Ho);let On;function tl(){return On||(On=so(el))}const sl=(...e)=>{const t=tl().createApp(...e),{mount:s}=t;return t.mount=n=>{const r=rl(n);if(!r)return;const i=t._component;!I(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=s(r,!1,nl(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function nl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function rl(e){return J(e)?document.querySelector(e):e}async function Lt(e,t={},s){return window.__TAURI_INTERNALS__.invoke(e,t,s)}const il={class:"container"},ol={key:0,class:"error-message"},ll={key:1,class:"loading"},cl={class:"section"},fl={class:"status-card"},ul={key:0,class:"success"},al=["disabled"],dl={class:"section"},hl={class:"status-card"},pl={class:"permission-status"},gl={class:"button-group"},_l=["disabled"],ml=["disabled"],bl={key:0,class:"permission-hint"},vl={key:1,class:"permission-hint error"},yl={key:2,class:"permission-hint warning"},xl={key:3,class:"permission-hint success"},Sl={class:"section"},wl={class:"stats-card"},Cl=["disabled"],Tl={key:0,class:"stats-grid"},El={class:"stat-item"},Ol={class:"stat-number"},Al={class:"stat-item"},Pl={class:"stat-number"},Rl={class:"stat-item"},Il={class:"stat-number"},Ml={key:1,class:"no-photos"},Fl=Ai({__name:"App",setup(e){const t=at(!1),s=at(""),n=at("Unknown"),r=at(null),i=at("");async function o(){try{t.value=!0,i.value="";const v=await Lt("test_swift_bridge");s.value=v}catch(v){i.value=`桥接测试失败: ${v}`}finally{t.value=!1}}async function c(){try{t.value=!0,i.value="";const v=await Lt("check_photo_permission");n.value=v}catch(v){i.value=`权限检查失败: ${v}`}finally{t.value=!1}}async function u(){try{t.value=!0,i.value="";const v=await Lt("request_photo_permission");n.value=v,(v==="Authorized"||v==="Limited")&&await h()}catch(v){i.value=`权限请求失败: ${v}`}finally{t.value=!1}}async function h(){try{t.value=!0,i.value="";const v=await Lt("get_photo_stats");r.value=v,n.value=v.permission_status}catch(v){i.value=`获取统计信息失败: ${v}`}finally{t.value=!1}}function a(v){switch(v){case"NotDetermined":return"未确定";case"Restricted":return"受限";case"Denied":return"拒绝";case"Authorized":return"已授权";case"Limited":return"有限访问";default:return"未知"}}function p(v){switch(v){case"Authorized":return"#4CAF50";case"Limited":return"#FF9800";case"Denied":case"Restricted":return"#F44336";default:return"#9E9E9E"}}return or(async()=>{await o(),await c(),await h()}),(v,x)=>(ue(),be("main",il,[x[9]||(x[9]=P("header",{class:"header"},[P("h1",null,"📸 相册管理器"),P("p",null,"基于 Tauri + Vue + Swift Bridge 构建")],-1)),i.value?(ue(),be("div",ol," ❌ "+Ge(i.value),1)):ke("",!0),t.value?(ue(),be("div",ll," 🔄 加载中... ")):ke("",!0),P("section",cl,[x[0]||(x[0]=P("h2",null,"🔗 Swift 桥接状态",-1)),P("div",fl,[s.value?(ue(),be("p",ul,"✅ "+Ge(s.value),1)):ke("",!0),P("button",{onClick:o,disabled:t.value,class:"btn btn-primary"}," 测试桥接功能 ",8,al)])]),P("section",dl,[x[4]||(x[4]=P("h2",null,"🔐 相册访问权限",-1)),P("div",hl,[P("div",pl,[x[1]||(x[1]=P("span",{class:"status-label"},"当前状态:",-1)),P("span",{class:"status-value",style:Zt({color:p(n.value)})},Ge(a(n.value)),5)]),P("div",gl,[P("button",{onClick:c,disabled:t.value,class:"btn btn-secondary"}," 检查权限 ",8,_l),P("button",{onClick:u,disabled:t.value||n.value==="Authorized",class:"btn btn-primary"}," 请求权限 ",8,ml)]),n.value==="NotDetermined"?(ue(),be("div",bl,x[2]||(x[2]=[Ts(" 💡 首次使用需要授权访问相册 ",-1),P("div",{class:"permission-guide"},[P("strong",null,"如果权限对话框没有显示："),P("ol",null,[P("li",null,"打开 系统设置 → 隐私与安全性 → 照片"),P("li",null,'点击 "+" 按钮添加应用'),P("li",null,"选择应用文件：src-tauri/target/debug/photo-gallery-app"),P("li",null,"启用权限开关")])],-1)]))):n.value==="Denied"?(ue(),be("div",vl,x[3]||(x[3]=[Ts(" ❌ 权限被拒绝，请在系统设置中手动授权 ",-1),P("div",{class:"permission-guide"},[P("strong",null,"手动授权步骤："),P("ol",null,[P("li",null,"打开 系统设置 → 隐私与安全性 → 照片"),P("li",null,'找到 "photo-gallery-app" 并启用权限'),P("li",null,'如果没有找到，点击 "+" 添加应用'),P("li",null,"重新启动应用")])],-1)]))):n.value==="Limited"?(ue(),be("div",yl," ⚠️ 仅有限访问权限，可能无法看到所有照片 ")):n.value==="Authorized"?(ue(),be("div",xl," ✅ 已获得完整相册访问权限 ")):ke("",!0)])]),P("section",Sl,[x[8]||(x[8]=P("h2",null,"📊 相册统计",-1)),P("div",wl,[P("button",{onClick:h,disabled:t.value,class:"btn btn-primary"}," 刷新统计 ",8,Cl),r.value?(ue(),be("div",Tl,[P("div",El,[P("div",Ol,Ge(r.value.total_count),1),x[5]||(x[5]=P("div",{class:"stat-label"},"总资源",-1))]),P("div",Al,[P("div",Pl,Ge(r.value.image_count),1),x[6]||(x[6]=P("div",{class:"stat-label"},"图片",-1))]),P("div",Rl,[P("div",Il,Ge(r.value.video_count),1),x[7]||(x[7]=P("div",{class:"stat-label"},"视频",-1))])])):ke("",!0),r.value&&r.value.total_count===0?(ue(),be("div",Ml," 📭 没有找到照片，可能是权限问题 ")):ke("",!0)])]),x[10]||(x[10]=xo('<section class="section" data-v-1d526125><h2 data-v-1d526125>📖 使用说明</h2><div class="info-card" data-v-1d526125><ol data-v-1d526125><li data-v-1d526125>首先测试 Swift 桥接功能是否正常</li><li data-v-1d526125>检查当前的相册访问权限状态</li><li data-v-1d526125>如果权限未授权，点击&quot;请求权限&quot;按钮</li><li data-v-1d526125>在弹出的系统对话框中选择&quot;允许&quot;</li><li data-v-1d526125>权限授权后，查看相册统计信息</li></ol><div class="note" data-v-1d526125><strong data-v-1d526125>注意：</strong>如果权限请求失败，可能需要在 macOS 系统设置中手动授权： <br data-v-1d526125>系统设置 → 安全性与隐私 → 隐私 → 照片 </div></div></section>',1))]))}}),Dl=(e,t)=>{const s=e.__vccOpts||e;for(const[n,r]of t)s[n]=r;return s},Nl=Dl(Fl,[["__scopeId","data-v-1d526125"]]);sl(Nl).mount("#app");
