{"$schema": "https://schema.tauri.app/config/2", "productName": "photo-gallery-app", "version": "0.1.0", "identifier": "com.photo-gallery-app.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "相册管理器", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "macOS": {"entitlements": "entitlements.plist", "frameworks": ["PhotoKit", "PhotosUI", "Foundation", "CoreFoundation", "AppKit"], "minimumSystemVersion": "10.15"}}}