use serde::{Deserialize, Serialize};
use system_photo_access::{
    call_swift_hello, check_permission_status_simple, debug_get_count_simple,
    get_asset_count_simple, request_permission_simple, test_function_simple,
};

// 权限状态枚举
#[derive(Debug, Serialize, Deserialize)]
pub enum PermissionStatus {
    NotDetermined,
    Restricted,
    Denied,
    Authorized,
    Limited,
    Unknown,
}

impl From<i32> for PermissionStatus {
    fn from(status: i32) -> Self {
        match status {
            0 => PermissionStatus::NotDetermined,
            1 => PermissionStatus::Restricted,
            2 => PermissionStatus::Denied,
            3 => PermissionStatus::Authorized,
            4 => PermissionStatus::Limited,
            _ => PermissionStatus::Unknown,
        }
    }
}

// 相册统计信息
#[derive(Debug, Serialize, Deserialize)]
pub struct PhotoStats {
    pub total_count: i32,
    pub image_count: i32,
    pub video_count: i32,
    pub permission_status: PermissionStatus,
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// 测试 Swift 桥接功能
#[tauri::command]
fn test_swift_bridge() -> Result<String, String> {
    call_swift_hello();
    let test_result = test_function_simple();

    if test_result == 12345 {
        Ok("Swift 桥接功能正常".to_string())
    } else {
        Err(format!(
            "Swift 桥接功能异常，期望 12345，实际 {}",
            test_result
        ))
    }
}

/// 检查相册访问权限状态
#[tauri::command]
fn check_photo_permission() -> Result<PermissionStatus, String> {
    let status = check_permission_status_simple();
    Ok(PermissionStatus::from(status))
}

/// 请求相册访问权限
#[tauri::command]
fn request_photo_permission() -> Result<PermissionStatus, String> {
    let status = request_permission_simple();
    Ok(PermissionStatus::from(status))
}

/// 获取相册统计信息
#[tauri::command]
fn get_photo_stats() -> Result<PhotoStats, String> {
    let permission_status = PermissionStatus::from(check_permission_status_simple());

    let (total_count, image_count, video_count) = match permission_status {
        PermissionStatus::Authorized | PermissionStatus::Limited => {
            let total = get_asset_count_simple(0); // 0 表示所有类型
            let images = get_asset_count_simple(1); // 1 表示图片
            let videos = get_asset_count_simple(2); // 2 表示视频
            (total, images, videos)
        }
        _ => {
            // 没有权限时尝试调试获取
            let debug_count = debug_get_count_simple();
            (debug_count, 0, 0)
        }
    };

    Ok(PhotoStats {
        total_count,
        image_count,
        video_count,
        permission_status,
    })
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_macos_permissions::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            test_swift_bridge,
            check_photo_permission,
            request_photo_permission,
            get_photo_stats
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
