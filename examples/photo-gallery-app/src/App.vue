<script setup lang="ts">
import { ref, onMounted } from "vue";
import { invoke } from "@tauri-apps/api/core";

// 权限状态枚举
enum PermissionStatus {
  NotDetermined = "NotDetermined",
  Restricted = "Restricted",
  Denied = "Denied",
  Authorized = "Authorized",
  Limited = "Limited",
  Unknown = "Unknown"
}

// 相册统计信息接口
interface PhotoStats {
  total_count: number;
  image_count: number;
  video_count: number;
  permission_status: PermissionStatus;
}

// 响应式数据
const loading = ref(false);
const bridgeStatus = ref("");
const permissionStatus = ref<PermissionStatus>(PermissionStatus.Unknown);
const photoStats = ref<PhotoStats | null>(null);
const errorMessage = ref("");

// 测试 Swift 桥接功能
async function testSwiftBridge() {
  try {
    loading.value = true;
    errorMessage.value = "";
    const result = await invoke<string>("test_swift_bridge");
    bridgeStatus.value = result;
  } catch (error) {
    errorMessage.value = `桥接测试失败: ${error}`;
  } finally {
    loading.value = false;
  }
}

// 检查权限状态
async function checkPermission() {
  try {
    loading.value = true;
    errorMessage.value = "";
    const status = await invoke<PermissionStatus>("check_photo_permission");
    permissionStatus.value = status;
  } catch (error) {
    errorMessage.value = `权限检查失败: ${error}`;
  } finally {
    loading.value = false;
  }
}

// 请求权限
async function requestPermission() {
  try {
    loading.value = true;
    errorMessage.value = "";
    const status = await invoke<PermissionStatus>("request_photo_permission");
    permissionStatus.value = status;

    // 权限请求后自动获取统计信息
    if (status === PermissionStatus.Authorized || status === PermissionStatus.Limited) {
      await getPhotoStats();
    }
  } catch (error) {
    errorMessage.value = `权限请求失败: ${error}`;
  } finally {
    loading.value = false;
  }
}

// 获取相册统计信息
async function getPhotoStats() {
  try {
    loading.value = true;
    errorMessage.value = "";
    const stats = await invoke<PhotoStats>("get_photo_stats");
    photoStats.value = stats;
    permissionStatus.value = stats.permission_status;
  } catch (error) {
    errorMessage.value = `获取统计信息失败: ${error}`;
  } finally {
    loading.value = false;
  }
}

// 获取权限状态的中文描述
function getPermissionStatusText(status: PermissionStatus): string {
  switch (status) {
    case PermissionStatus.NotDetermined:
      return "未确定";
    case PermissionStatus.Restricted:
      return "受限";
    case PermissionStatus.Denied:
      return "拒绝";
    case PermissionStatus.Authorized:
      return "已授权";
    case PermissionStatus.Limited:
      return "有限访问";
    default:
      return "未知";
  }
}

// 获取权限状态的颜色
function getPermissionStatusColor(status: PermissionStatus): string {
  switch (status) {
    case PermissionStatus.Authorized:
      return "#4CAF50";
    case PermissionStatus.Limited:
      return "#FF9800";
    case PermissionStatus.Denied:
    case PermissionStatus.Restricted:
      return "#F44336";
    default:
      return "#9E9E9E";
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await testSwiftBridge();
  await checkPermission();
  await getPhotoStats();
});
</script>

<template>
  <main class="container">
    <header class="header">
      <h1>📸 相册管理器</h1>
      <p>基于 Tauri + Vue + Swift Bridge 构建</p>
    </header>

    <!-- 错误信息 -->
    <div v-if="errorMessage" class="error-message">
      ❌ {{ errorMessage }}
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      🔄 加载中...
    </div>

    <!-- Swift 桥接状态 -->
    <section class="section">
      <h2>🔗 Swift 桥接状态</h2>
      <div class="status-card">
        <p v-if="bridgeStatus" class="success">✅ {{ bridgeStatus }}</p>
        <button @click="testSwiftBridge" :disabled="loading" class="btn btn-primary">
          测试桥接功能
        </button>
      </div>
    </section>

    <!-- 权限管理 -->
    <section class="section">
      <h2>🔐 相册访问权限</h2>
      <div class="status-card">
        <div class="permission-status">
          <span class="status-label">当前状态:</span>
          <span
            class="status-value"
            :style="{ color: getPermissionStatusColor(permissionStatus) }"
          >
            {{ getPermissionStatusText(permissionStatus) }}
          </span>
        </div>

        <div class="button-group">
          <button @click="checkPermission" :disabled="loading" class="btn btn-secondary">
            检查权限
          </button>
          <button
            @click="requestPermission"
            :disabled="loading || permissionStatus === 'Authorized'"
            class="btn btn-primary"
          >
            请求权限
          </button>
        </div>

        <!-- 权限说明 -->
        <div v-if="permissionStatus === 'NotDetermined'" class="permission-hint">
          💡 首次使用需要授权访问相册
          <div class="permission-guide">
            <strong>如果权限对话框没有显示：</strong>
            <ol>
              <li>打开 系统设置 → 隐私与安全性 → 照片</li>
              <li>点击 "+" 按钮添加应用</li>
              <li>选择应用文件：src-tauri/target/debug/photo-gallery-app</li>
              <li>启用权限开关</li>
            </ol>
          </div>
        </div>
        <div v-else-if="permissionStatus === 'Denied'" class="permission-hint error">
          ❌ 权限被拒绝，请在系统设置中手动授权
          <div class="permission-guide">
            <strong>手动授权步骤：</strong>
            <ol>
              <li>打开 系统设置 → 隐私与安全性 → 照片</li>
              <li>找到 "photo-gallery-app" 并启用权限</li>
              <li>如果没有找到，点击 "+" 添加应用</li>
              <li>重新启动应用</li>
            </ol>
          </div>
        </div>
        <div v-else-if="permissionStatus === 'Limited'" class="permission-hint warning">
          ⚠️ 仅有限访问权限，可能无法看到所有照片
        </div>
        <div v-else-if="permissionStatus === 'Authorized'" class="permission-hint success">
          ✅ 已获得完整相册访问权限
        </div>
      </div>
    </section>

    <!-- 相册统计 -->
    <section class="section">
      <h2>📊 相册统计</h2>
      <div class="stats-card">
        <button @click="getPhotoStats" :disabled="loading" class="btn btn-primary">
          刷新统计
        </button>

        <div v-if="photoStats" class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ photoStats.total_count }}</div>
            <div class="stat-label">总资源</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ photoStats.image_count }}</div>
            <div class="stat-label">图片</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ photoStats.video_count }}</div>
            <div class="stat-label">视频</div>
          </div>
        </div>

        <div v-if="photoStats && photoStats.total_count === 0" class="no-photos">
          📭 没有找到照片，可能是权限问题
        </div>
      </div>
    </section>

    <!-- 使用说明 -->
    <section class="section">
      <h2>📖 使用说明</h2>
      <div class="info-card">
        <ol>
          <li>首先测试 Swift 桥接功能是否正常</li>
          <li>检查当前的相册访问权限状态</li>
          <li>如果权限未授权，点击"请求权限"按钮</li>
          <li>在弹出的系统对话框中选择"允许"</li>
          <li>权限授权后，查看相册统计信息</li>
        </ol>

        <div class="note">
          <strong>注意：</strong>如果权限请求失败，可能需要在 macOS 系统设置中手动授权：
          <br>系统设置 → 安全性与隐私 → 隐私 → 照片
        </div>
      </div>
    </section>
  </main>
</template>

<style scoped>
.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.header p {
  color: #7f8c8d;
  font-size: 14px;
}

.section {
  margin-bottom: 30px;
}

.section h2 {
  color: #34495e;
  margin-bottom: 15px;
  font-size: 18px;
}

.status-card, .stats-card, .info-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.error-message {
  background: #fee;
  color: #c53030;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #fed7d7;
}

.loading {
  text-align: center;
  color: #4299e1;
  font-weight: 500;
  margin: 20px 0;
}

.success {
  color: #38a169;
  font-weight: 500;
}

.permission-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.status-label {
  font-weight: 500;
  color: #4a5568;
}

.status-value {
  font-weight: 600;
  font-size: 16px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #4299e1;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #3182ce;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
  background: #cbd5e0;
}

.permission-hint {
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  margin-top: 10px;
}

.permission-hint.success {
  background: #f0fff4;
  color: #38a169;
  border: 1px solid #9ae6b4;
}

.permission-hint.warning {
  background: #fffbf0;
  color: #d69e2e;
  border: 1px solid #fbd38d;
}

.permission-hint.error {
  background: #fff5f5;
  color: #e53e3e;
  border: 1px solid #fed7d7;
}

.permission-guide {
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  font-size: 13px;
}

.permission-guide strong {
  display: block;
  margin-bottom: 8px;
  color: #2d3748;
}

.permission-guide ol {
  margin: 0;
  padding-left: 20px;
}

.permission-guide li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f7fafc;
  border-radius: 8px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.no-photos {
  text-align: center;
  color: #a0aec0;
  font-style: italic;
  margin-top: 20px;
}

.info-card ol {
  margin: 0 0 20px 0;
  padding-left: 20px;
}

.info-card li {
  margin-bottom: 8px;
  color: #4a5568;
}

.note {
  background: #edf2f7;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid #4299e1;
  font-size: 14px;
  color: #2d3748;
}

@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .button-group {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #2d3748;
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  .status-card, .stats-card, .info-card {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
  }

  .stat-item {
    background: #4a5568 !important;
  }

  .note {
    background: #2d3748 !important;
    color: #e2e8f0 !important;
  }
}
</style>